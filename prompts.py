# 提示模板和示例
import langextract as lx
import textwrap

# 人员信息提取提示模板
PERSON_EXTRACTION_PROMPT = textwrap.dedent("""
    你是一个专业的信息提取专家。请根据提供的原始人员数据和维基百科数据，提取和更新人员的详细信息。

    任务要求：
    1. 对比原始数据和维基百科数据
    2. 提取准确、完整的人员信息
    3. 优先使用维基百科的权威信息
    4. 保持数据格式的一致性
    5. 对于缺失的信息，保持空字符串

    提取字段包括：
    - 基本信息：姓名、性别、生日、国籍、种族、民族
    - 联系信息：电话、邮箱、工作地点
    - 教育经历：学校、专业、学位、毕业时间
    - 工作经历：公司、职位、部门、工作时间
    - 其他信息：政治团体、宗教信仰、语言、服兵役情况

    请确保提取的信息准确、完整，并按照指定格式输出。
""")

# Few-shot学习示例
EXTRACTION_EXAMPLES = [
    lx.data.ExampleData(
        text=textwrap.dedent("""
        原始数据：{"original_name": "张三", "nationality": "中国", "sex": "", "birthday": ""}
        
        维基百科数据：
        张三（1985年3月15日-），中国著名企业家，男性，汉族。毕业于清华大学计算机科学与技术专业，获得学士学位。
        现任某科技公司CEO，曾在微软公司担任高级工程师。联系电话：138****1234，邮箱：<EMAIL>。
        """),
        extractions=[
            lx.data.Extraction(
                extraction_class="basic_info",
                extraction_text="张三（1985年3月15日-），中国著名企业家，男性，汉族",
                attributes={
                    "original_name": "张三",
                    "birthday": "1985年3月15日",
                    "nationality": "中国",
                    "sex": "男",
                    "ethnic": "汉族"
                }
            ),
            lx.data.Extraction(
                extraction_class="education",
                extraction_text="毕业于清华大学计算机科学与技术专业，获得学士学位",
                attributes={
                    "school": "清华大学",
                    "major": "计算机科学与技术",
                    "degree": "学士"
                }
            ),
            lx.data.Extraction(
                extraction_class="work_experience",
                extraction_text="现任某科技公司CEO，曾在微软公司担任高级工程师",
                attributes={
                    "current_company": "某科技公司",
                    "current_job": "CEO",
                    "previous_company": "微软公司",
                    "previous_job": "高级工程师"
                }
            ),
            lx.data.Extraction(
                extraction_class="contact_info",
                extraction_text="联系电话：138****1234，邮箱：<EMAIL>",
                attributes={
                    "mobile_number": "138****1234",
                    "email": "<EMAIL>"
                }
            )
        ]
    ),
    
    lx.data.ExampleData(
        text=textwrap.dedent("""
        原始数据：{"original_name": "李四", "nationality": "美国", "education": [{"school": "哈佛大学"}]}
        
        维基百科数据：
        李四博士，1978年出生于美国加州，华裔美国人。在哈佛大学获得MBA学位，专业为工商管理。
        目前在谷歌公司担任产品经理，工作地点位于加州山景城。
        """),
        extractions=[
            lx.data.Extraction(
                extraction_class="basic_info",
                extraction_text="李四博士，1978年出生于美国加州，华裔美国人",
                attributes={
                    "original_name": "李四",
                    "birthday": "1978年",
                    "nationality": "美国",
                    "race": "华裔",
                    "working_place": "加州"
                }
            ),
            lx.data.Extraction(
                extraction_class="education",
                extraction_text="在哈佛大学获得MBA学位，专业为工商管理",
                attributes={
                    "school": "哈佛大学",
                    "degree": "MBA",
                    "major": "工商管理"
                }
            ),
            lx.data.Extraction(
                extraction_class="work_experience",
                extraction_text="目前在谷歌公司担任产品经理，工作地点位于加州山景城",
                attributes={
                    "company_Name": "谷歌公司",
                    "job": "产品经理",
                    "working_place": "加州山景城"
                }
            )
        ]
    )
]

def get_extraction_prompt():
    """获取提取提示模板"""
    return PERSON_EXTRACTION_PROMPT

def get_extraction_examples():
    """获取Few-shot学习示例"""
    return EXTRACTION_EXAMPLES
