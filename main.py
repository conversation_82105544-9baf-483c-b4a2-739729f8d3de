#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WikiUpdate LangExtract版本
使用LangExtract替代Dify工作流进行人员信息提取和更新

使用方法：
python main.py --start-time "2024-01-01 00:00:00" [--end-time "2024-01-02 00:00:00"]
"""

import argparse
import logging
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from process import DataProcessor

logger = logging.getLogger('langextract_updater')

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='WikiUpdate LangExtract版本')

    parser.add_argument(
        '--start-time',
        required=True,
        help='开始时间，格式: "YYYY-MM-DD HH:MM:SS"'
    )

    parser.add_argument(
        '--end-time',
        help='结束时间，格式: "YYYY-MM-DD HH:MM:SS"，默认为当前时间'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=10,
        help='批处理大小，默认: 10'
    )

    parser.add_argument(
        '--max-workers',
        type=int,
        default=5,
        help='最大并发数，默认: 5'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别，默认: INFO'
    )

    return parser.parse_args()

def validate_time_format(time_str: str) -> bool:
    """验证时间格式"""
    try:
        datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        return True
    except ValueError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("WikiUpdate LangExtract版本")
    print("使用LangExtract进行人员信息提取和更新")
    print("=" * 60)

    # 解析参数
    args = parse_arguments()

    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 验证时间格式
    if not validate_time_format(args.start_time):
        logger.error("开始时间格式错误，请使用: YYYY-MM-DD HH:MM:SS")
        sys.exit(1)

    if args.end_time and not validate_time_format(args.end_time):
        logger.error("结束时间格式错误，请使用: YYYY-MM-DD HH:MM:SS")
        sys.exit(1)

    logger.info("配置信息:")
    logger.info(f"  LLM模型: Qwen3-30B-A3B-Instruct-2507")
    logger.info(f"  LLM地址: http://192.168.100.182:8002/v1")
    logger.info(f"  ES地址: http://90.90.69.60:9200")
    logger.info(f"  批处理大小: {args.batch_size}")
    logger.info(f"  最大并发数: {args.max_workers}")
    logger.info(f"  开始时间: {args.start_time}")
    logger.info(f"  结束时间: {args.end_time or '当前时间'}")

    try:
        # 创建处理器并开始处理
        processor = DataProcessor()

        import time
        start_time = time.time()
        processor.process_data_by_time_range(args.start_time, args.end_time)
        end_time = time.time()

        elapsed_time = end_time - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)

        logger.info(f"处理完成，总耗时: {int(hours)}小时{int(minutes)}分钟{int(seconds)}秒")
        logger.info(f"结果已保存到: results/update_results.csv")

    except KeyboardInterrupt:
        logger.info("用户中断处理")
        sys.exit(0)
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
