<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1756178771597_syde9f0gl" time="2025/08/26 03:26">
    <content>
      Google LangExtract是一个Python库，用于从非结构化文本中提取结构化信息。核心特性：
      1. 使用LLM进行信息提取，支持Google Gemini、OpenAI GPT、本地Ollama模型
      2. 基于prompt和examples的few-shot学习方式
      3. 支持实体提取、关系提取、属性标注
      4. 提供可视化和JSONL格式输出
      5. 支持长文档处理（多pass、并行处理、智能分块）
      6. 核心API：lx.extract(text_or_documents, prompt_description, examples, model_id)
      7. 数据结构：ExampleData(text, extractions), Extraction(extraction_class, extraction_text, attributes)
      8. 可替代传统NER工具，更灵活的自定义提取规则
    </content>
    <tags>#最佳实践 #工具使用</tags>
  </item>
</memory>