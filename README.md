# WikiUpdate LangExtract版本

使用LangExtract替代Dify工作流进行人员信息提取和更新的重构版本。

## 功能特性

- ✅ 使用本地vllm部署的Qwen模型
- ✅ 基于LangExtract的结构化信息提取
- ✅ 支持时间范围查询和批量处理
- ✅ 完整的错误处理和重试机制
- ✅ CSV格式的处理结果记录
- ✅ 详细的日志记录和进度监控

## 项目结构

```
wikiupdate_langextract/
├── main.py              # 主程序入口
├── config.py            # 配置文件
├── prompts.py           # 提示模板和示例
├── utils.py             # 工具类
├── process.py           # 核心处理模块
├── requirements.txt     # 依赖包
├── README.md           # 说明文档
└── results/            # 结果输出目录
    ├── update_results.csv
    └── process.log
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

主要配置在 `config.py` 中：

- **LLM配置**: 本地vllm部署的Qwen模型
- **Elasticsearch配置**: 数据源配置
- **处理参数**: 批处理大小、并发数等

## 使用方法

### 基本用法

```bash
# 处理指定时间范围的数据
python main.py --start-time "2024-01-01 00:00:00"

# 指定结束时间
python main.py --start-time "2024-01-01 00:00:00" --end-time "2024-01-02 00:00:00"
```

### 高级参数

```bash
python main.py \
  --start-time "2024-01-01 00:00:00" \
  --end-time "2024-01-02 00:00:00" \
  --batch-size 20 \
  --max-workers 10 \
  --log-level DEBUG
```

### 参数说明

- `--start-time`: 开始时间（必需），格式: "YYYY-MM-DD HH:MM:SS"
- `--end-time`: 结束时间（可选），默认为当前时间
- `--batch-size`: 批处理大小，默认10
- `--max-workers`: 最大并发数，默认5
- `--log-level`: 日志级别，可选: DEBUG, INFO, WARNING, ERROR

## 处理流程

1. **数据获取**: 从Elasticsearch按时间范围获取目标数据
2. **维基查询**: 调用Wiki API获取人员的维基百科信息
3. **信息提取**: 使用LangExtract对比原始数据和维基数据，提取更新信息
4. **数据合并**: 将提取结果与原始数据合并
5. **图片处理**: 处理相关图片信息（如有）
6. **数据更新**: 更新Elasticsearch中的文档
7. **结果记录**: 将处理结果记录到CSV文件

## 输出结果

### CSV结果文件

位置: `results/update_results.csv`

包含字段:
- 目标名称
- 处理状态 (success/error/not_found)
- 消息
- 文档ID
- 图片URL
- 处理时间

### 日志文件

位置: `results/process.log`

包含详细的处理日志，便于问题排查。

## 使用示例

```bash
# 处理最近一天的数据
python main.py --start-time "2024-12-25 00:00:00"

# 处理特定时间段的数据
python main.py --start-time "2024-12-20 00:00:00" --end-time "2024-12-25 23:59:59"
```