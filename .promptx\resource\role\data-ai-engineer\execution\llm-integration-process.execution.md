<execution>
  <constraint>
    ## LLM集成客观限制
    - **API调用限制**：请求频率限制、并发连接数限制、Token使用限制
    - **模型能力约束**：上下文长度限制、输出格式约束、推理能力边界
    - **网络延迟约束**：API响应时间、网络超时、重试机制限制
    - **成本控制约束**：Token使用成本、API调用费用、计算资源成本
    - **数据隐私约束**：敏感数据处理限制、数据传输安全要求
    - **模型版本约束**：模型更新频率、向后兼容性、版本管理复杂性
  </constraint>

  <rule>
    ## LLM集成强制规则
    - **输入验证必须**：所有输入数据必须经过格式和安全验证
    - **输出验证必须**：所有LLM输出必须经过格式和内容验证
    - **错误处理完备**：必须处理所有可能的API错误和异常情况
    - **重试机制必须**：必须实现指数退避的重试机制
    - **日志记录完整**：必须记录所有API调用和响应的详细日志
    - **成本监控必须**：必须监控和控制LLM使用成本
    - **安全传输必须**：敏感数据传输必须使用加密通道
  </rule>

  <guideline>
    ## LLM集成指导原则
    - **提示工程标准化**：建立标准的提示模板和few-shot示例
    - **批量处理优化**：尽可能使用批量API调用提高效率
    - **缓存策略应用**：对相同输入的结果进行缓存避免重复调用
    - **降级策略准备**：准备LLM服务不可用时的降级方案
    - **结果验证多层**：建立多层次的结果验证和质量检查
    - **版本管理规范**：对提示模板和模型版本进行规范管理
    - **性能监控持续**：持续监控LLM调用的性能和质量指标
  </guideline>

  <process>
    ## LLM集成标准流程
    
    ### 提示工程阶段
    ```mermaid
    graph TD
        A[需求分析] --> B[提示设计]
        B --> C[示例准备]
        C --> D[格式定义]
        D --> E[测试验证]
        E --> F[优化迭代]
        
        E -->|验证失败| G[问题分析]
        G --> B
        F --> H[模板固化]
    ```
    
    **具体步骤**：
    1. **需求明确**：明确提取任务的具体要求和期望输出格式
    2. **提示设计**：设计清晰、具体的提示词模板
    3. **示例构建**：准备高质量的few-shot学习示例
    4. **格式规范**：定义输入输出的标准格式和验证规则
    5. **测试验证**：使用测试数据验证提示效果
    6. **迭代优化**：基于测试结果优化提示和示例
    
    ### API集成阶段
    ```mermaid
    graph TD
        A[客户端配置] --> B[认证设置]
        B --> C[请求封装]
        C --> D[响应解析]
        D --> E[错误处理]
        E --> F[重试机制]
        
        D -->|解析失败| G[格式验证]
        E -->|错误分类| H[降级处理]
    ```
    
    **具体步骤**：
    1. **客户端初始化**：配置API客户端和连接参数
    2. **认证管理**：设置API密钥和认证机制
    3. **请求构建**：构建标准化的API请求格式
    4. **响应处理**：解析和验证API响应结果
    5. **异常处理**：分类处理各种API错误和异常
    6. **重试策略**：实现智能的重试和退避机制
    
    ### 批量处理阶段
    ```mermaid
    graph TD
        A[任务分批] --> B[并发控制]
        B --> C[API调用]
        C --> D[结果收集]
        D --> E[质量检查]
        E --> F[结果合并]
        
        C -->|调用失败| G[重试队列]
        E -->|质量不达标| H[人工审核]
    ```
    
    **具体步骤**：
    1. **任务分批**：将大批量任务分解为合适大小的批次
    2. **并发管理**：控制并发API调用数量避免超限
    3. **调用执行**：执行API调用并收集响应结果
    4. **结果验证**：验证每个响应的格式和内容质量
    5. **质量控制**：对低质量结果进行标记和处理
    6. **结果整合**：将批次结果合并为最终输出
    
    ### 质量保证阶段
    ```mermaid
    graph TD
        A[输出验证] --> B[格式检查]
        B --> C[内容审核]
        C --> D[一致性检查]
        D --> E[质量评分]
        E --> F[反馈优化]
        
        C -->|内容异常| G[人工介入]
        D -->|不一致| H[重新处理]
    ```
    
    **质量检查点**：
    - **格式合规性**：检查输出是否符合预定义格式
    - **内容完整性**：验证提取内容的完整性和准确性
    - **逻辑一致性**：检查提取结果的逻辑一致性
    - **置信度评估**：评估提取结果的可信度
    - **异常检测**：识别和处理异常的提取结果
    
    ### 监控优化阶段
    ```mermaid
    graph TD
        A[性能监控] --> B[成本分析]
        B --> C[质量统计]
        C --> D[趋势分析]
        D --> E[优化建议]
        E --> F[策略调整]
    ```
    
    **监控指标**：
    - **调用成功率**：API调用的成功率统计
    - **响应时间**：API调用的平均响应时间
    - **Token使用量**：输入输出Token的使用统计
    - **成本分析**：API调用的成本分析和预测
    - **质量指标**：提取结果的质量评估指标
  </process>

  <criteria>
    ## LLM集成质量标准
    
    ### API调用质量标准
    - ✅ **成功率**：API调用成功率 ≥ 99%
    - ✅ **响应时间**：平均响应时间 ≤ 5秒
    - ✅ **重试成功率**：重试机制成功率 ≥ 95%
    - ✅ **错误处理覆盖**：错误类型处理覆盖率 = 100%
    
    ### 提取质量标准
    - ✅ **格式正确率**：输出格式正确率 ≥ 98%
    - ✅ **内容准确率**：提取内容准确率 ≥ 95%
    - ✅ **完整性**：信息提取完整率 ≥ 90%
    - ✅ **一致性**：多次提取结果一致率 ≥ 85%
    
    ### 性能效率标准
    - ✅ **吞吐量**：单位时间处理文档数达到设计目标
    - ✅ **并发能力**：支持设计的并发API调用数量
    - ✅ **资源利用率**：系统资源使用率在合理范围
    - ✅ **成本效率**：单位处理成本在预算范围内
    
    ### 可靠性标准
    - ✅ **服务可用性**：LLM服务可用性 ≥ 99.5%
    - ✅ **降级机制**：服务降级时功能可用性 ≥ 80%
    - ✅ **数据安全**：敏感数据处理安全性 = 100%
    - ✅ **版本兼容性**：模型版本更新兼容性 ≥ 95%
  </criteria>
</execution>
