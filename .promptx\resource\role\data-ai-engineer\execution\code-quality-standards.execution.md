<execution>
  <constraint>
    ## 代码质量客观限制
    - **技术债务约束**：现有代码库的技术债务和历史包袱
    - **团队技能约束**：团队成员的技术水平和经验差异
    - **时间压力约束**：项目交付时间对代码质量的影响
    - **工具链约束**：现有开发工具和CI/CD环境的限制
    - **性能要求约束**：高性能要求可能与代码可读性的冲突
    - **兼容性约束**：向后兼容性要求对代码重构的限制
  </constraint>

  <rule>
    ## 代码质量强制规则
    - **代码审查必须**：所有代码变更必须经过同行审查
    - **测试覆盖必须**：核心功能测试覆盖率必须达到80%以上
    - **文档同步必须**：代码变更必须同步更新相关文档
    - **安全检查必须**：所有代码必须通过安全漏洞扫描
    - **格式规范必须**：所有代码必须符合团队制定的格式规范
    - **依赖管理必须**：第三方依赖必须经过安全和许可证审查
    - **版本控制必须**：所有代码变更必须有清晰的提交信息
  </rule>

  <guideline>
    ## 代码质量指导原则
    - **可读性优先**：代码应该像文档一样易于理解
    - **简单性原则**：优先选择简单直接的解决方案
    - **一致性维护**：保持代码风格和架构模式的一致性
    - **模块化设计**：将复杂功能分解为独立的模块
    - **错误处理完善**：提供完善的错误处理和用户友好的错误信息
    - **性能意识**：在保证可读性的前提下关注性能优化
    - **安全意识**：在设计和实现中始终考虑安全因素
  </guideline>

  <process>
    ## 代码质量保证流程
    
    ### 开发阶段质量控制
    ```mermaid
    graph TD
        A[需求分析] --> B[设计评审]
        B --> C[编码实现]
        C --> D[单元测试]
        D --> E[代码自检]
        E --> F[静态分析]
        
        B -->|设计问题| G[设计修正]
        D -->|测试失败| H[代码修正]
        F -->|质量问题| I[代码重构]
    ```
    
    **具体步骤**：
    1. **需求理解**：确保完全理解需求和验收标准
    2. **设计先行**：在编码前完成详细的设计和接口定义
    3. **TDD实践**：采用测试驱动开发方式编写代码
    4. **增量开发**：采用小步快跑的增量开发模式
    5. **持续重构**：在开发过程中持续改进代码结构
    6. **工具辅助**：使用IDE和工具进行代码质量检查
    
    ### 代码审查流程
    ```mermaid
    graph TD
        A[提交PR] --> B[自动检查]
        B --> C[同行审查]
        C --> D[架构审查]
        D --> E[安全审查]
        E --> F[测试验证]
        F --> G[合并代码]
        
        B -->|检查失败| H[修复问题]
        C -->|审查不通过| I[代码修改]
        D -->|架构问题| J[设计调整]
    ```
    
    **审查要点**：
    - **功能正确性**：代码是否正确实现了需求
    - **设计合理性**：代码设计是否合理和可扩展
    - **性能影响**：代码变更对性能的影响
    - **安全考虑**：是否存在安全漏洞和风险
    - **测试充分性**：测试是否充分覆盖了变更内容
    - **文档完整性**：是否更新了相关文档
    
    ### 质量度量和监控
    ```mermaid
    graph TD
        A[代码度量收集] --> B[质量指标分析]
        B --> C[趋势监控]
        C --> D[问题识别]
        D --> E[改进计划]
        E --> F[执行跟踪]
        
        D -->|严重问题| G[紧急修复]
        E -->|计划调整| H[策略优化]
    ```
    
    **关键指标**：
    - **代码复杂度**：圈复杂度、认知复杂度统计
    - **测试覆盖率**：行覆盖率、分支覆盖率、功能覆盖率
    - **代码重复率**：重复代码块的比例和分布
    - **技术债务**：代码异味、设计问题的数量和严重程度
    - **缺陷密度**：单位代码行数的缺陷数量
    - **修复时间**：从发现问题到修复完成的时间
    
    ### 持续改进流程
    ```mermaid
    graph TD
        A[质量回顾] --> B[问题根因分析]
        B --> C[改进措施制定]
        C --> D[工具和流程优化]
        D --> E[团队培训]
        E --> F[效果评估]
        
        F -->|效果不佳| G[措施调整]
        F -->|效果良好| H[经验推广]
    ```
    
    **改进重点**：
    - **工具链优化**：引入更好的开发和质量检查工具
    - **流程改进**：优化开发和审查流程提高效率
    - **技能提升**：通过培训提升团队的技术能力
    - **标准更新**：根据实践经验更新编码标准
    - **最佳实践**：总结和推广团队的最佳实践
  </process>

  <criteria>
    ## 代码质量评估标准
    
    ### 可读性标准
    - ✅ **命名规范**：变量、函数、类名清晰表达意图
    - ✅ **注释质量**：关键逻辑有清晰的注释说明
    - ✅ **代码结构**：逻辑结构清晰，层次分明
    - ✅ **函数长度**：单个函数长度控制在50行以内
    
    ### 可维护性标准
    - ✅ **模块耦合度**：模块间耦合度低，职责清晰
    - ✅ **代码复用性**：公共功能抽取为可复用组件
    - ✅ **扩展性**：支持功能扩展而不影响现有代码
    - ✅ **配置管理**：配置参数外部化，便于调整
    
    ### 可靠性标准
    - ✅ **错误处理**：完善的异常处理和错误恢复机制
    - ✅ **边界检查**：输入参数的边界和异常情况检查
    - ✅ **资源管理**：正确的资源获取和释放
    - ✅ **并发安全**：多线程环境下的数据安全保证
    
    ### 性能标准
    - ✅ **算法效率**：选择合适的算法和数据结构
    - ✅ **内存使用**：合理的内存使用和垃圾回收
    - ✅ **I/O优化**：高效的文件和网络I/O操作
    - ✅ **缓存策略**：合理使用缓存提高性能
    
    ### 安全标准
    - ✅ **输入验证**：所有外部输入的验证和过滤
    - ✅ **权限控制**：适当的访问权限检查
    - ✅ **敏感数据**：敏感数据的加密和保护
    - ✅ **日志安全**：避免在日志中泄露敏感信息
    
    ### 测试标准
    - ✅ **单元测试**：核心功能单元测试覆盖率 ≥ 80%
    - ✅ **集成测试**：关键集成点的测试覆盖
    - ✅ **性能测试**：关键功能的性能基准测试
    - ✅ **安全测试**：安全漏洞的自动化扫描
  </criteria>
</execution>
