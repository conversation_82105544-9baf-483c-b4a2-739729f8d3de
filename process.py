# 核心处理模块
import json
import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import langextract as lx
from tqdm import tqdm

import config
import prompts
import utils

logger = logging.getLogger('langextract_updater')

class PersonExtractor:
    """人员信息提取器"""
    
    def __init__(self):
        self.prompt = prompts.get_extraction_prompt()
        self.examples = prompts.get_extraction_examples()
        
    def extract_person_info(self, original_data: Dict, wiki_data: str, name: str, country: str) -> Dict:
        """使用LangExtract提取人员信息"""
        try:
            # 构建输入文本
            input_text = self._build_input_text(original_data, wiki_data, name, country)
            
            # 调用LangExtract
            result = lx.extract(
                text_or_documents=input_text,
                prompt_description=self.prompt,
                examples=self.examples,
                language_model_type=lx.inference.OpenAILanguageModel,  # 使用OpenAI兼容接口
                model_id=config.LLM_MODEL_NAME,
                api_key="dummy",  # 本地模型不需要真实key
                base_url=config.LLM_API_BASE_URL,
                fence_output=True,
                use_schema_constraints=False,
                timeout=config.REQUEST_TIMEOUT
            )
            
            # 处理提取结果
            return self._process_extraction_result(result, original_data)
            
        except Exception as e:
            logger.error(f"提取 {name} 信息时出错: {e}")
            return {"status": "error", "message": f"提取失败: {str(e)}", "data": original_data}
    
    def _build_input_text(self, original_data: Dict, wiki_data: str, name: str, country: str) -> str:
        """构建输入文本"""
        # 移除敏感字段
        clean_original = {k: v for k, v in original_data.items() 
                         if k not in ['_document_id', 'photo', 'putin_time']}
        
        input_text = f"""
        目标人员：{name}
        国家/地区：{country}
        
        原始数据：
        {json.dumps(clean_original, ensure_ascii=False, indent=2)}
        
        维基百科数据：
        {wiki_data if wiki_data else "未找到维基百科信息"}
        
        请根据以上信息提取和更新人员的详细信息。
        """
        
        return input_text.strip()
    
    def _process_extraction_result(self, result, original_data: Dict) -> Dict:
        """处理提取结果"""
        try:
            if not result or not hasattr(result, 'extractions'):
                return {"status": "error", "message": "提取结果为空", "data": original_data}
            
            # 初始化更新数据
            updated_data = utils.ensure_complete_fields(original_data.copy())
            
            # 处理提取的实体
            for extraction in result.extractions:
                if extraction.attributes:
                    self._merge_extraction_attributes(updated_data, extraction.attributes)
            
            return {
                "status": "success",
                "message": "信息提取成功",
                "data": updated_data
            }
            
        except Exception as e:
            logger.error(f"处理提取结果时出错: {e}")
            return {"status": "error", "message": f"结果处理失败: {str(e)}", "data": original_data}
    
    def _merge_extraction_attributes(self, target_data: Dict, attributes: Dict):
        """合并提取的属性到目标数据"""
        for key, value in attributes.items():
            if key in target_data and value and str(value).strip():
                # 处理教育和工作经历的特殊情况
                if key in ['education', 'working'] and isinstance(target_data[key], list):
                    if target_data[key] and isinstance(target_data[key][0], dict):
                        # 更新第一个条目
                        if isinstance(value, dict):
                            target_data[key][0].update(value)
                        else:
                            # 如果是字符串，尝试解析相关字段
                            self._parse_complex_field(target_data[key][0], key, str(value))
                else:
                    target_data[key] = value
    
    def _parse_complex_field(self, target_dict: Dict, field_type: str, value: str):
        """解析复杂字段"""
        # 简单的关键词匹配解析
        if field_type == 'education':
            if '大学' in value or '学院' in value:
                # 提取学校名称
                for word in value.split():
                    if '大学' in word or '学院' in word:
                        target_dict['school'] = word
                        break
        elif field_type == 'working':
            if '公司' in value or '集团' in value:
                # 提取公司名称
                for word in value.split():
                    if '公司' in word or '集团' in word:
                        target_dict['company_Name'] = word
                        break

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.es_client = utils.ElasticsearchClient()
        self.wiki_client = utils.WikiAPIClient()
        self.extractor = PersonExtractor()
        self.image_handler = utils.ImageHandler()
        
    def process_data_by_time_range(self, start_time: str, end_time: str = None):
        """按时间范围处理数据"""
        # 转换时间为时间戳
        start_timestamp = int(datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000)
        end_timestamp = int(datetime.now().timestamp() * 1000) if not end_time else int(datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S").timestamp() * 1000)
        
        logger.info(f"开始处理时间范围: {start_time} 到 {end_time or 'now'}")
        
        # 获取数据
        data = self.es_client.get_data_by_time_range(start_timestamp, end_timestamp)
        
        if not data:
            logger.info("没有找到需要处理的数据")
            return
        
        # 统计信息
        stats = {
            "total": len(data),
            "success": 0,
            "error": 0,
            "not_found": 0
        }
        
        logger.info(f"开始处理 {stats['total']} 个目标")
        
        # 处理每个目标
        with tqdm(total=stats['total'], desc="处理进度") as pbar:
            for idx, (name, target_data) in enumerate(data.items(), 1):
                logger.info(f"处理第 {idx}/{stats['total']} 个目标: {name}")
                
                try:
                    result = self._process_single_target(name, target_data)
                    
                    # 更新统计
                    if result['status'] == 'success':
                        stats['success'] += 1
                    elif result['status'] == 'not_found':
                        stats['not_found'] += 1
                    else:
                        stats['error'] += 1
                    
                    # 保存到CSV
                    utils.save_to_csv(
                        name=name,
                        status=result['status'],
                        message=result['message'],
                        document_id=target_data.get('_document_id', ''),
                        image_url=result.get('image_url', '')
                    )
                    
                    # 更新进度条
                    pbar.update(1)
                    pbar.set_postfix({
                        "成功": stats['success'],
                        "错误": stats['error'],
                        "未找到": stats['not_found']
                    })
                    
                    # 请求间隔
                    time.sleep(config.SLEEP_BETWEEN_REQUESTS)
                    
                except Exception as e:
                    logger.error(f"处理目标 {name} 时发生异常: {e}")
                    stats['error'] += 1
                    utils.save_to_csv(name, "exception", str(e), target_data.get('_document_id', ''))
                    pbar.update(1)
        
        # 输出最终统计
        logger.info("处理完成!")
        logger.info(f"统计信息: 总计: {stats['total']}, 成功: {stats['success']}, 错误: {stats['error']}, 未找到: {stats['not_found']}")
    
    def _process_single_target(self, name: str, target_data: Dict) -> Dict:
        """处理单个目标"""
        document_id = target_data.get('_document_id')
        country = target_data.get('nationality', '')
        
        if not document_id:
            return {"status": "error", "message": "缺少文档ID"}
        
        # 1. 获取维基数据
        wiki_data = self.wiki_client.get_wiki_data(name, country)
        if not wiki_data:
            return {"status": "not_found", "message": "未找到维基百科信息"}
        
        # 2. 确保字段完整
        complete_data = utils.ensure_complete_fields(target_data)
        
        # 3. 使用LangExtract提取信息
        extraction_result = self.extractor.extract_person_info(complete_data, wiki_data, name, country)
        
        if extraction_result['status'] != 'success':
            return extraction_result
        
        updated_data = extraction_result['data']
        
        # 4. 处理图片（如果有）
        image_url = ""
        # 这里可以添加图片处理逻辑
        
        # 5. 更新ES文档
        if self.es_client.update_document(document_id, updated_data):
            return {
                "status": "success",
                "message": "更新成功",
                "image_url": image_url
            }
        else:
            return {"status": "error", "message": "ES更新失败"}
