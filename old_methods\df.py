# 数据处理模块

import json

import copy

import os

import time

import logging

import requests

from typing import Dict, List, Optional, Any, Union

from elasticsearch import Elasticsearch

from datetime import datetime

from api import DifyAPI,update_photo

from tqdm import tqdm  # 添加tqdm进度条库



# 设置日志

logging.basicConfig(

    level=logging.INFO,

    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',

    handlers=[

        logging.FileHandler('update_process.log', encoding='utf-8'),

        logging.StreamHandler()

    ]

)

logger = logging.getLogger('dify_updater')



# 初始化 Dify API 客户端

dify_client = DifyAPI()



# 连接 Elasticsearch

try:

    es = Elasticsearch("http://90.90.69.60:9200", http_auth=('root', 'lenovo123456!'),timeout=120,max_retries=2,retry_on_timeout=True)

    if not es.ping():

        logger.error("无法连接到 Elasticsearch 服务器")

except Exception as e:

    logger.error(f"Elasticsearch 连接错误: {e}")

    es = None





def load_target_data(filename: str = 'tlist.json') -> Dict[str, Any]:

    """加载目标数据



    Args:

        filename (str): 目标数据文件名



    Returns:

        dict: 目标数据字典

    """

    try:

        with open(filename, 'r', encoding='utf8') as f:

            data = json.loads(f.read())

        logger.info(f"已加载{len(data)}条目标数据")

        return data

    except Exception as e:

        logger.error(f"加载目标数据失败: {e}")

        return {}





def dify_api(target, raw_json):

    """调用 Dify API 获取数据



    Args:

        target (str): 目标名称

        raw_json (dict): 目标原始数据



    Returns:

        dict: API 响应结果, 包含状态和数据

    """

    try:

        # 使用 DifyAPI 类发送请求

        response = dify_client.chat(target, raw_json)



        # 解析响应

        result = dify_client.parse_response(response)



        return result

    except Exception as e:

        logger.error(f"调用 Dify API 时出错: {e}")

        return {

            "status": "error",

            "message": f"API 调用异常: {e}",

            "data": None

        }





def update_es_document(document_id, update_data,request_timeout=180):

    """更新 Elasticsearch 中的文档



    Args:

        document_id (str): 文档ID

        update_data (dict): 更新的数据



    Returns:

        bool: 更新是否成功

    """

    if not es:

        logger.error("Elasticsearch 连接不可用，无法更新文档")

        return False



    try:

        # 复制数据以避免修改原始数据

        data_to_update = copy.deepcopy(update_data)



        # 移除文档 ID 字段，避免更新冲突

        if "_document_id" in data_to_update:

            del data_to_update["_document_id"]



        # 更新文档

        response = es.update(

            index="3_targetmap_entity",

            id=document_id,

            body={"doc": data_to_update},

            request_timeout=request_timeout

        )



        logger.info(f"文档 {document_id} 更新成功: {response['result']}")

        return True



    except Exception as e:

        logger.error(f"更新文档 {document_id} 时出错: {e}")

        return False





def full_attrs(target_data):

    try:

        target_data.pop("_document_id", None)

    except Exception as e:

        print(e)

    example = {

        "birthday": "",  # 年龄 (Age) - 通常存储出生日期字符串

        "sex": "",  # 性别 (Gender) - 例如 "Male", "Female", "Other"

        "race": "",  # 种族 (Race/Ethnicity)

        "ethnic": "",  # 民族 (Ethnic Group)

        "nationality": "",  # 国籍 (Nationality)

        "nick_name": "",  # 别名 (Nickname)

        "original_name": "",  # 曾用名 (Original Name)

        "political_group": "",  # 政党 (Political Party)

        "education": [{  # 学习经历 (Educational Experience) - 对象结构

            "degree": "",  # 学位

            "diploma": "",  # 学历

            "education_complete_date": "",  # 毕业日期 (lowercase variant)

            "education_start_date": "",  # 入学日期 (lowercase variant)

            "graduate_School": "",  # 毕业院校

            "major": "",  # 专业

            "school": ""  # 学校

        }

        ],

        "working_place": "",  # 工作地点 (Workplace) - 通常是地址字符串

        "mobile_number": "",  # 电话号码 (Mobile Phone Number)

        "office_number": "",  # 办公电话 (lowercase variant)

        "fax_Number": "",  # 传真号码 (Fax Number)

        "email": "",  # 电子邮箱 (Email Address)

        "religion_belief": "",  # 宗教信仰 (Religious Belief)

        "is_christianism": "",  # 是否是基督教 (Boolean or Integer 0/1, based on mapping type 'long') - 使用 null 表示未知或不适用

        "language": "",  # 语言习惯 (Language Habits) - 例如 "Mandarin", "English"

        "enlistment": "",  # 服兵役情况 (Military Service Status)

        "working": [{  # 工作经历 (Work Experience) - 对象结构

            "company_Name": "",  # 公司名称

            "employeeNumber": "",  # 员工编号

            "job": "",  # 职位

            "work_Department": "",  # 工作部门

            "work_EndDate": "",  # 工作结束日期

            "work_Start_Date": "",  # 工作开始日期

            "work_Type": "",  # 工作类型 (e.g., Full-time, Part-time)

        }]

    }



    def ensure_keys_present(source_dict, dest_dict):

        for key, source_value in source_dict.items():

            if key not in dest_dict:

                # If the key is missing in the destination dictionary

                if isinstance(source_value, dict):

                    # If the source value is a dictionary, deep copy it

                    # This ensures nested structures are fully created

                    dest_dict[key] = copy.deepcopy(source_value)

                else:

                    # Otherwise, just assign the default value (e.g., "")

                    dest_dict[key] = source_value

            else:

                # If the key exists in the destination dictionary

                # Check if both source and destination values are dictionaries

                if isinstance(source_value, dict) and isinstance(dest_dict.get(key), dict):

                    # If both are dictionaries, recurse into the nested structure

                    ensure_keys_present(source_value, dest_dict[key])

                # Optional: Handle type mismatch if needed.

                # For example, if source expects a dict but dest has a non-dict,

                # you might choose to overwrite dest_dict[key], log a warning, or ignore.

                # Current behavior: If types mismatch (e.g., dest[key] is not a dict

                # when source_value is), it won't recurse or overwrite.



    # Start the recursive process

    ensure_keys_present(example, target_data)

    # --- End of the logic requested ---



    # The function modifies target_data in-place.

    # Returning it is convenient for chaining or assignment.

    return target_data





def load_process_state(checkpoint_file: str = 'checkpoint.json') -> Dict[str, Any]:

    """加载处理状态



    Args:

        checkpoint_file (str): 检查点文件名



    Returns:

        dict: 处理状态信息

    """

    if not os.path.exists(checkpoint_file):

        return {

            'processed_names': [],

            'stats': {

                "total": 0,

                "success": 0,

                "not_same_person": 0,

                "error": 0,

                "unknown": 0

            },

            'last_index': 0,

            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")

        }



    try:

        with open(checkpoint_file, 'r', encoding='utf-8') as f:

            data = json.load(f)

        logger.info(f"已加载检查点文件，上次处理到第 {data['last_index']} 个目标")

        return data

    except Exception as e:

        logger.error(f"加载检查点文件失败: {e}")

        return {

            'processed_names': [],

            'stats': {

                "total": 0,

                "success": 0,

                "not_same_person": 0,

                "error": 0,

                "unknown": 0

            },

            'last_index': 0,

            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")

        }





def save_process_state(state: Dict[str, Any], checkpoint_file: str = 'checkpoint.json') -> bool:

    """保存处理状态



    Args:

        state (dict): 处理状态信息

        checkpoint_file (str): 检查点文件名



    Returns:

        bool: 保存是否成功

    """

    try:

        with open(checkpoint_file, 'w', encoding='utf-8') as f:

            json.dump(state, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:

        logger.error(f"保存检查点文件失败: {e}")

        return False







if __name__ == "__main__":

    # 创建结果目录

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    results_dir = f"results/results_{timestamp}"

    if not os.path.exists(results_dir):

        os.makedirs(results_dir)



    # 加载处理状态

    checkpoint_file = 'checkpoint.json'

    process_state = load_process_state(checkpoint_file)



    # 如果是新的处理任务，使用当前时间戳

    if process_state['last_index'] == 0:

        process_state['timestamp'] = timestamp

    else:

        timestamp = process_state['timestamp']

        # 如果是继续之前的任务，使用之前的时间戳创建目录

        results_dir = f"results_{timestamp}"

        if not os.path.exists(results_dir):

            os.makedirs(results_dir)



    # 设置统计文件路径

    stats_file = f"{results_dir}/update_stats_{timestamp}.csv"



    # 如果是新任务，创建统计文件头

    if process_state['last_index'] == 0:

        with open(stats_file, 'w', encoding='utf-8') as f:

            f.write("目标名称,处理状态,消息,文档ID,image_url\n")



    # 获取统计信息

    stats = process_state['stats']



    # 加载目标数据

    target_file = "tlist.json"

    target_data = load_target_data(target_file)

    if not target_data:

        logger.error("无法加载目标数据，程序退出")

        exit(1)



    # 获取所有目标名称

    names = list(target_data.keys())

    total_targets = len(names)



    # 获取之前已处理的目标名称

    processed_names = set(process_state['processed_names'])



    # 确定起始索引

    start_index = process_state['last_index']



    logger.info(f"开始处理 {total_targets} 个目标，从第 {start_index + 1} 个开始")



    # 记录开始时间

    start_time = time.time()



    # 使用tqdm创建进度条，设置初始值为已处理的数量

    with tqdm(total=total_targets, desc="处理进度", unit="目标", initial=start_index) as pbar:

        # 处理每个目标，从上次中断的位置继续

        for idx, name in enumerate(names[start_index:], start_index + 1):

            logger.info(f"处理第 {idx}/{total_targets} 个目标: {name}")



            # 如果此目标已经处理过，跳过

            if name in processed_names:

                logger.info(f"目标 {name} 已处理过，跳过")

                pbar.update(1)

                continue



            stats["total"] += 1



            try:

                # 获取目标的原始数据

                target_obj = target_data[name]

                document_id = target_obj.get("_document_id")



                if not document_id:

                    logger.warning(f"目标 {name} 缺少文档ID，跳过")

                    status = "error"

                    message = "缺少文档ID"

                else:

                    # 确保数据结构完整

                    target_obj_with_full_attrs = full_attrs(target_obj)



                    # 调用 Dify API

                    api_result = dify_api(name, target_obj_with_full_attrs)

                    message = api_result.get('message')

                    status = api_result["status"]

                    # 根据 API 结果处理

                    if status == "success" and api_result["data"]:

                        # 合并原始数据和 API 返回的数据

                        if isinstance(api_result["data"], dict):

                            # 直接合并字典,以api_result为主，并将image_url以base64形式导入

                            updated_data = {**target_obj_with_full_attrs, **api_result["data"]}

                            image_url = api_result.get("image_url", None)

                            if image_url != None:

                                updated_data['photo'] = update_photo(image_url)

                                message += f"{message} - {name}有图片"

                            else:

                                message += f"{message} - {name}无图片"

                        else:

                            # 如果不是字典，进一步处理或保存原始响应

                            try:

                                result = json.loads(api_result["data"])

                                inner_json = json.loads(result["text"])

                                updated_data = {**target_obj_with_full_attrs, **inner_json}

                                image_url = api_result.get("image_url", None)

                                if image_url != None:

                                    updated_data['photo'] = update_photo(image_url)

                                    message += f"{message} - {name}有图片"

                                else:

                                    message += f"{message} - {name}无图片"

                            except:

                                updated_data = target_obj_with_full_attrs

                                image_url = api_result.get("image_url", None)

                                message = f"{message} - 未更新-解析response失败！"

                                status = "error"

                            # updated_data["_dify_response"] = api_result["data"]



                        # 更新 ES 文档

                        if update_es_document(document_id, updated_data):

                            stats["success"] += 1

                            # 保存更新后的数据到结果目录

                            safe_name = "".join([c if c.isalnum() or c in "._- " else "_" for c in name])

                            with open(f"{results_dir}/{safe_name}_updated.json", 'w', encoding='utf-8') as f:

                                json.dump(updated_data, f, ensure_ascii=False, indent=2)

                        else:

                            status = "error"

                            message = "ES更新失败"

                            stats["error"] += 1



                    elif status == "not_same_person":

                        stats["not_same_person"] += 1



                    elif status == "error":

                        stats["error"] += 1



                    else:

                        stats["unknown"] += 1



                # 记录当前目标的处理结果

                with open(stats_file, 'a', encoding='utf-8') as f:

                    # 获取image_url值，如果不存在则为空字符串

                    img_url = api_result.get("image_url", "") if 'api_result' in locals() and isinstance(api_result, dict) else ""

                    f.write(f"{name},{status},{message},{document_id},{img_url}\n")



                # 更新处理状态

                processed_names.add(name)

                process_state['processed_names'] = list(processed_names)

                process_state['last_index'] = idx

                process_state['stats'] = stats



                # 每处理5个目标，保存一次检查点

                if idx % 5 == 0:

                    save_process_state(process_state, checkpoint_file)



                # 每处理10个目标，输出一次统计信息

                if idx % 10 == 0 or idx == total_targets:

                    # 计算剩余时间估计

                    elapsed_time = time.time() - start_time

                    items_processed = idx - start_index

                    items_per_second = items_processed / elapsed_time if elapsed_time > 0 else 0

                    remaining_items = total_targets - idx

                    remaining_time = remaining_items / items_per_second if items_per_second > 0 else 0



                    # 格式化剩余时间

                    hours, remainder = divmod(remaining_time, 3600)

                    minutes, seconds = divmod(remainder, 60)

                    remaining_time_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"



                    logger.info(f"进度: {idx}/{total_targets}, "

                                f"成功: {stats['success']}, "

                                f"不匹配: {stats['not_same_person']}, "

                                f"错误: {stats['error']}, "

                                f"未知: {stats['unknown']}, "

                                f"预计剩余时间: {remaining_time_str}")



                # 更新进度条

                pbar.update(1)

                pbar.set_postfix({

                    "成功": stats['success'],

                    "错误": stats['error'],

                    "不匹配": stats['not_same_person']

                })



                # 简单的速率限制，避免过于频繁请求 API

                time.sleep(1)



            except Exception as e:

                logger.error(f"处理目标 {name} 时发生异常: {e}")

                # 记录错误

                with open(stats_file, 'a', encoding='utf-8') as f:

                    f.write(f"{name},exception,{str(e)},未知\n")

                stats["error"] += 1



                # 更新处理状态

                processed_names.add(name)

                process_state['processed_names'] = list(processed_names)

                process_state['last_index'] = idx

                process_state['stats'] = stats

                save_process_state(process_state, checkpoint_file)



                # 更新进度条

                pbar.update(1)



    # 处理完成，保存最终检查点

    process_state['last_index'] = total_targets

    save_process_state(process_state, checkpoint_file)



    # 处理完成，输出最终统计

    total_elapsed_time = time.time() - start_time

    hours, remainder = divmod(total_elapsed_time, 3600)

    minutes, seconds = divmod(remainder, 60)

    elapsed_time_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"



    logger.info("所有目标处理完成!")

    logger.info(f"统计信息: 总计: {stats['total']}, "

                f"成功: {stats['success']}, "

                f"不匹配: {stats['not_same_person']}, "

                f"错误: {stats['error']}, "

                f"未知: {stats['unknown']}")

    logger.info(f"总耗时: {elapsed_time_str}")

    logger.info(f"详细结果保存在 {stats_file}")



    # 处理完成后，删除检查点文件

    if os.path.exists(checkpoint_file):

        try:

            os.remove(checkpoint_file)

            logger.info("处理完成，已删除检查点文件")

        except Exception as e:

            logger.warning(f"无法删除检查点文件: {e}")

