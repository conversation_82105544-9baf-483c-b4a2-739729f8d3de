<execution>
  <constraint>
    ## 数据工程客观限制
    - **数据质量约束**：输入数据的完整性、准确性、一致性限制
    - **处理性能约束**：内存限制、CPU限制、I/O带宽限制
    - **存储容量约束**：磁盘空间、数据库连接数、索引大小限制
    - **网络延迟约束**：API调用延迟、数据传输带宽、超时限制
    - **并发处理约束**：线程池大小、连接池限制、锁竞争问题
    - **成本控制约束**：API调用费用、存储成本、计算资源成本
  </constraint>

  <rule>
    ## 数据工程强制规则
    - **数据完整性保证**：必须确保数据处理过程中的完整性和一致性
    - **错误处理完备**：必须对所有可能的错误情况进行处理和记录
    - **状态可恢复**：必须支持处理中断后的状态恢复和断点续传
    - **监控可观测**：必须提供详细的处理进度和状态监控
    - **资源清理**：必须确保处理完成后的资源清理和连接释放
    - **幂等性保证**：重复执行相同操作必须产生相同结果
    - **事务一致性**：批量操作必须保证事务的原子性
  </rule>

  <guideline>
    ## 数据工程指导原则
    - **分层处理设计**：将复杂的数据处理分解为清晰的层次
    - **批量优化优先**：优先使用批量操作提高处理效率
    - **增量处理支持**：设计支持增量数据处理的机制
    - **缓存策略应用**：合理使用缓存减少重复计算和I/O
    - **并行处理利用**：在保证数据一致性的前提下利用并行处理
    - **配置外部化**：将处理参数和配置外部化便于调整
    - **日志记录详细**：提供详细的处理日志便于问题排查
  </guideline>

  <process>
    ## 数据工程标准流程
    
    ### 数据获取阶段
    ```mermaid
    graph TD
        A[数据源连接] --> B[数据查询/提取]
        B --> C[数据验证]
        C --> D[数据清洗]
        D --> E[数据格式化]
        E --> F[数据暂存]
        
        C -->|验证失败| G[错误记录]
        D -->|清洗失败| G
        E -->|格式化失败| G
    ```
    
    **具体步骤**：
    1. **连接建立**：建立与数据源的稳定连接，配置连接池和超时参数
    2. **数据提取**：使用分页或游标方式提取数据，避免内存溢出
    3. **数据验证**：验证数据格式、必填字段、数据类型的正确性
    4. **数据清洗**：处理缺失值、异常值、重复数据
    5. **格式标准化**：统一数据格式、编码、时间格式等
    6. **临时存储**：将处理后的数据存储到临时区域
    
    ### 数据处理阶段
    ```mermaid
    graph TD
        A[处理任务分解] --> B[批量处理]
        B --> C[结果验证]
        C --> D[错误处理]
        D --> E[状态更新]
        E --> F[进度报告]
        
        C -->|验证通过| G[结果暂存]
        C -->|验证失败| H[重试机制]
        H --> B
    ```
    
    **具体步骤**：
    1. **任务分解**：将大任务分解为可管理的小批次
    2. **批量处理**：按批次处理数据，控制内存使用和处理时间
    3. **结果验证**：验证处理结果的正确性和完整性
    4. **异常处理**：对处理失败的数据进行分类和重试
    5. **状态管理**：更新处理状态，支持断点续传
    6. **进度监控**：实时报告处理进度和统计信息
    
    ### 数据存储阶段
    ```mermaid
    graph TD
        A[存储准备] --> B[数据转换]
        B --> C[批量写入]
        C --> D[写入验证]
        D --> E[索引更新]
        E --> F[清理临时数据]
        
        D -->|验证失败| G[回滚操作]
        G --> H[错误记录]
    ```
    
    **具体步骤**：
    1. **存储准备**：准备目标存储环境，检查存储空间和权限
    2. **数据转换**：将数据转换为目标存储格式
    3. **批量写入**：使用批量操作提高写入效率
    4. **写入验证**：验证数据是否正确写入目标存储
    5. **索引维护**：更新相关索引和统计信息
    6. **资源清理**：清理临时文件和释放资源
    
    ### 质量保证流程
    ```mermaid
    graph TD
        A[数据质量检查] --> B[处理质量验证]
        B --> C[性能指标监控]
        C --> D[错误率统计]
        D --> E[质量报告生成]
        E --> F[改进建议]
    ```
    
    **质量检查点**：
    - **数据完整性**：检查数据是否完整，无丢失
    - **数据准确性**：验证数据处理的准确性
    - **处理效率**：监控处理速度和资源使用
    - **错误处理**：统计和分析错误类型和频率
    - **系统稳定性**：监控系统运行状态和异常情况
  </process>

  <criteria>
    ## 数据工程质量标准
    
    ### 数据质量标准
    - ✅ **完整性**：数据处理完成率 ≥ 99.5%
    - ✅ **准确性**：数据处理准确率 ≥ 99.9%
    - ✅ **一致性**：数据格式一致性 = 100%
    - ✅ **及时性**：数据处理延迟 ≤ 预期时间的120%
    
    ### 系统性能标准
    - ✅ **吞吐量**：单位时间处理记录数达到设计目标
    - ✅ **延迟**：单条记录处理时间在可接受范围内
    - ✅ **资源利用率**：CPU、内存、磁盘使用率在合理范围
    - ✅ **并发能力**：支持设计的并发处理数量
    
    ### 可靠性标准
    - ✅ **错误恢复**：系统故障后能自动或手动恢复
    - ✅ **数据一致性**：处理中断后数据状态保持一致
    - ✅ **监控覆盖**：关键指标监控覆盖率 = 100%
    - ✅ **日志完整性**：关键操作日志记录完整率 = 100%
    
    ### 可维护性标准
    - ✅ **代码质量**：代码复杂度、测试覆盖率达标
    - ✅ **文档完整性**：技术文档、操作手册完整
    - ✅ **配置管理**：配置参数外部化、版本化管理
    - ✅ **部署自动化**：支持自动化部署和回滚
  </criteria>
</execution>
