# 配置文件
import os
from datetime import datetime

# LLM配置 - 使用本地vllm部署的qwen模型
LLM_API_BASE_URL = "http://***************:8002/v1"
LLM_MODEL_NAME = "Qwen3-30B-A3B-Instruct-2507"

# Elasticsearch配置
ES_HOST = "http://***********:9200"
ES_AUTH = ('root', 'lenovo123456!')
ES_INDEX = "3_targetmap_entity"
ES_TIMEOUT = 120
ES_MAX_RETRIES = 2

# Wiki API配置
WIKI_API_BASE_URL = "http://**************:8080/wiki"

# 处理配置
BATCH_SIZE = 10  # 批处理大小
MAX_WORKERS = 5  # 最大并发数
REQUEST_TIMEOUT = 600  # 请求超时时间(秒)
RETRY_ATTEMPTS = 3  # 重试次数
SLEEP_BETWEEN_REQUESTS = 1  # 请求间隔(秒)

# 输出配置
RESULTS_DIR = "results"
CSV_FILENAME = "update_results.csv"
LOG_FILENAME = "process.log"

# 确保结果目录存在
if not os.path.exists(RESULTS_DIR):
    os.makedirs(RESULTS_DIR)

# 数据字段模板
PERSON_FIELDS_TEMPLATE = {
    "birthday": "",
    "sex": "",
    "race": "",
    "ethnic": "",
    "nationality": "",
    "nick_name": "",
    "original_name": "",
    "political_group": "",
    "education": [{
        "degree": "",
        "diploma": "",
        "education_complete_date": "",
        "education_start_date": "",
        "graduate_School": "",
        "major": "",
        "school": ""
    }],
    "working_place": "",
    "mobile_number": "",
    "office_number": "",
    "fax_Number": "",
    "email": "",
    "religion_belief": "",
    "is_christianism": "",
    "language": "",
    "enlistment": "",
    "working": [{
        "company_Name": "",
        "employeeNumber": "",
        "job": "",
        "work_Department": "",
        "work_EndDate": "",
        "work_Start_Date": "",
        "work_Type": ""
    }]
}
