# LangGraph Server Configuration
LANGSMITH_API_KEY=your_langsmith_api_key_here

# News Agent Configuration
# 新闻检索 API
NEWS_API_BASE_URL=http://192.168.100.135:8001

# Instruct模型 API (快速摘要)
LLM_INSTRUCT_API_BASE_URL=http://192.168.100.182:8002/v1
LLM_INSTRUCT_MODEL_NAME=Qwen3-30B-A3B-Instruct-2507

# Thinking模型 API (深度分析)
LLM_THINKING_API_BASE_URL=http://192.168.100.182:8003/v1
LLM_THINKING_MODEL_NAME=Qwen3-30B-A3B-Thinking-2507

# Embedding模型 API
EMBEDDING_API_ENDPOINT=http://192.168.100.182:8005/v1/embeddings
EMBEDDING_MODEL_NAME=Qwen3-Embedding-0.6B

# 其他工具API (包含process-text接口)
OTHER_API_BASE_URL=http://192.168.100.135:8001

# Qdrant向量数据库
QDRANT_URL=http://192.168.100.182:16333
QDRANT_COLLECTION_NAME=news_collection

# OpenAI API Key (用于LangChain兼容)
OPENAI_API_KEY=null

# 处理参数
EMBEDDING_BATCH_SIZE=32
HDBSCAN_MIN_CLUSTER_SIZE=3
HDBSCAN_MIN_SAMPLES=5

# 噪声处理配置
ENABLE_NOISE_PROCESSING=true
NOISE_CLUSTER_ID=-1

# 报告输出目录
REPORTS_DIR=./reports
