#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本
验证各个组件的连接和配置是否正确
"""

import sys
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """测试依赖包导入"""
    logger.info("测试依赖包导入...")
    
    try:
        import langextract as lx
        logger.info("✅ LangExtract导入成功")
    except ImportError as e:
        logger.error(f"❌ LangExtract导入失败: {e}")
        return False
    
    try:
        from elasticsearch import Elasticsearch
        logger.info("✅ Elasticsearch导入成功")
    except ImportError as e:
        logger.error(f"❌ Elasticsearch导入失败: {e}")
        return False
    
    try:
        import requests
        logger.info("✅ Requests导入成功")
    except ImportError as e:
        logger.error(f"❌ Requests导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    logger.info("测试配置文件...")
    
    try:
        import config
        logger.info("✅ 配置文件导入成功")
        logger.info(f"  LLM API: {config.LLM_API_BASE_URL}")
        logger.info(f"  LLM Model: {config.LLM_MODEL_NAME}")
        logger.info(f"  ES Host: {config.ES_HOST}")
        logger.info(f"  ES Index: {config.ES_INDEX}")
        return True
    except Exception as e:
        logger.error(f"❌ 配置文件测试失败: {e}")
        return False

def test_elasticsearch():
    """测试Elasticsearch连接"""
    logger.info("测试Elasticsearch连接...")
    
    try:
        from utils import ElasticsearchClient
        es_client = ElasticsearchClient()
        logger.info("✅ Elasticsearch连接成功")
        return True
    except Exception as e:
        logger.error(f"❌ Elasticsearch连接失败: {e}")
        return False

def test_wiki_api():
    """测试Wiki API"""
    logger.info("测试Wiki API...")
    
    try:
        from utils import WikiAPIClient
        wiki_client = WikiAPIClient()
        
        # 测试获取数据
        result = wiki_client.get_wiki_data("测试", "中国")
        if result is not None:
            logger.info("✅ Wiki API连接成功")
        else:
            logger.warning("⚠️ Wiki API连接成功但未返回数据")
        return True
    except Exception as e:
        logger.error(f"❌ Wiki API测试失败: {e}")
        return False

def test_llm_connection():
    """测试LLM连接"""
    logger.info("测试LLM连接...")
    
    try:
        import requests
        import config
        
        # 测试LLM服务是否可达
        response = requests.get(f"{config.LLM_API_BASE_URL}/models", timeout=10)
        if response.status_code == 200:
            logger.info("✅ LLM服务连接成功")
            return True
        else:
            logger.warning(f"⚠️ LLM服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ LLM连接测试失败: {e}")
        return False

def test_prompts():
    """测试提示模板"""
    logger.info("测试提示模板...")
    
    try:
        from prompts import get_extraction_prompt, get_extraction_examples
        
        prompt = get_extraction_prompt()
        examples = get_extraction_examples()
        
        if prompt and examples:
            logger.info("✅ 提示模板加载成功")
            logger.info(f"  提示模板长度: {len(prompt)} 字符")
            logger.info(f"  示例数量: {len(examples)}")
            return True
        else:
            logger.error("❌ 提示模板或示例为空")
            return False
    except Exception as e:
        logger.error(f"❌ 提示模板测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("WikiUpdate LangExtract 配置测试")
    logger.info("=" * 50)
    
    tests = [
        ("依赖包导入", test_imports),
        ("配置文件", test_config),
        ("Elasticsearch连接", test_elasticsearch),
        ("Wiki API", test_wiki_api),
        ("LLM连接", test_llm_connection),
        ("提示模板", test_prompts),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 {test_name}测试...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总:")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统配置正确。")
        return True
    else:
        logger.warning("⚠️ 部分测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
