{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-26T03:31:25.706Z", "updatedAt": "2025-08-26T03:31:25.723Z", "resourceCount": 7}, "resources": [{"id": "data-ai-engineer", "source": "project", "protocol": "role", "name": "Data Ai Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/data-ai-engineer/data-ai-engineer.role.md", "metadata": {"createdAt": "2025-08-26T03:31:25.709Z", "updatedAt": "2025-08-26T03:31:25.709Z", "scannedAt": "2025-08-26T03:31:25.709Z", "path": "role/data-ai-engineer/data-ai-engineer.role.md"}}, {"id": "code-quality-standards", "source": "project", "protocol": "execution", "name": "Code Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-ai-engineer/execution/code-quality-standards.execution.md", "metadata": {"createdAt": "2025-08-26T03:31:25.717Z", "updatedAt": "2025-08-26T03:31:25.717Z", "scannedAt": "2025-08-26T03:31:25.717Z", "path": "role/data-ai-engineer/execution/code-quality-standards.execution.md"}}, {"id": "data-engineering-workflow", "source": "project", "protocol": "execution", "name": "Data Engineering Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-ai-engineer/execution/data-engineering-workflow.execution.md", "metadata": {"createdAt": "2025-08-26T03:31:25.718Z", "updatedAt": "2025-08-26T03:31:25.718Z", "scannedAt": "2025-08-26T03:31:25.718Z", "path": "role/data-ai-engineer/execution/data-engineering-workflow.execution.md"}}, {"id": "llm-integration-process", "source": "project", "protocol": "execution", "name": "Llm Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-ai-engineer/execution/llm-integration-process.execution.md", "metadata": {"createdAt": "2025-08-26T03:31:25.719Z", "updatedAt": "2025-08-26T03:31:25.719Z", "scannedAt": "2025-08-26T03:31:25.719Z", "path": "role/data-ai-engineer/execution/llm-integration-process.execution.md"}}, {"id": "architecture-design", "source": "project", "protocol": "thought", "name": "Architecture Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-ai-engineer/thought/architecture-design.thought.md", "metadata": {"createdAt": "2025-08-26T03:31:25.721Z", "updatedAt": "2025-08-26T03:31:25.721Z", "scannedAt": "2025-08-26T03:31:25.720Z", "path": "role/data-ai-engineer/thought/architecture-design.thought.md"}}, {"id": "system-thinking", "source": "project", "protocol": "thought", "name": "System Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-ai-engineer/thought/system-thinking.thought.md", "metadata": {"createdAt": "2025-08-26T03:31:25.721Z", "updatedAt": "2025-08-26T03:31:25.721Z", "scannedAt": "2025-08-26T03:31:25.721Z", "path": "role/data-ai-engineer/thought/system-thinking.thought.md"}}, {"id": "technical-analysis", "source": "project", "protocol": "thought", "name": "Technical Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-ai-engineer/thought/technical-analysis.thought.md", "metadata": {"createdAt": "2025-08-26T03:31:25.722Z", "updatedAt": "2025-08-26T03:31:25.722Z", "scannedAt": "2025-08-26T03:31:25.722Z", "path": "role/data-ai-engineer/thought/technical-analysis.thought.md"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 1, "execution": 3, "thought": 3}, "bySource": {"project": 7}}}