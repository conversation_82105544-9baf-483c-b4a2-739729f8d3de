【身份验证指令】
判断target JSON与wiki_extract是否描述同一人物，仅输出以下JSON结果之一：
{"is_same_person": "true"} 
或 
{"is_same_person": "false"}

【判定规则（适度宽松）】
1. 基础匹配：如target中非空字段（如英文名、出生年份、职业等）与wiki内容无明显冲突，即初步成立
2. 时间验证：
   - 若target中标明出生年份，且wiki中时间不冲突（相差±5年内或wiki未明示），则接受
   - 若wiki人物为已过世的古代人物（出生<1900），而target有明显当代特征（社交媒体/近年活动），则视为不同人
3. 宽容确认（适用于信息稀缺场景）：
   - 若target字段填写率低于50%，但wiki中有2项以上明显特征（如：姓名+职业+国籍）无矛盾，即可成立
4. 活跃平台：若target中出现社交媒体信息，但wiki未提及，不构成冲突，默认忽略此维度
5. 否决条件：
   - 出现明显关键信息矛盾（如职业/国籍/生卒年完全不符）
   - 同名人物但分别来自完全不同领域（如一位为歌手，另一位为政治学者）

【执行要求】
- 验证优先级：基础匹配 > 时间验证 > 否决条件 > 宽容确认
- 对1950年后出生者默认添加“当代人物”过滤标签
- 严格输出以下JSON格式之一：
  {"is_same_person": "true"} 或 {"is_same_person": "false"}
- 禁止输出任何解释、注释或额外文字


【特殊说明】
- 若wiki条目更新时间早于target账号创建时间，允许wiki缺失部分数字足迹或更新滞后
- 对于政治家、演员等常见重名职业，若无明显冲突且信息匹配度中等，即默认通过
- 若存在社交媒体handle匹配则强判定为同一人物

【input data】
输入target: {{target_raw_json}}
wiki_extract: {{extract}}
输出: {"is_same_person": "true"} 或 {"is_same_person": "false"}