# 工具类
import json
import copy
import logging
import requests
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any
from elasticsearch import Elasticsearch
import config

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{config.RESULTS_DIR}/{config.LOG_FILENAME}', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('langextract_updater')

class ElasticsearchClient:
    """Elasticsearch客户端"""
    
    def __init__(self):
        self.es = Elasticsearch(
            config.ES_HOST,
            http_auth=config.ES_AUTH,
            timeout=config.ES_TIMEOUT,
            max_retries=config.ES_MAX_RETRIES,
            retry_on_timeout=True
        )
        if not self.es.ping():
            raise ConnectionError("无法连接到 Elasticsearch 服务器")
    
    def get_data_by_time_range(self, start_timestamp_ms: int, end_timestamp_ms: int) -> Dict[str, Any]:
        """根据时间范围获取数据"""
        logger.info(f"获取时间范围数据: {start_timestamp_ms} -> {end_timestamp_ms}")
        
        query = {
            "query": {
                "range": {
                    "putin_time": {
                        "gte": start_timestamp_ms,
                        "lt": end_timestamp_ms
                    }
                }
            }
        }
        
        # 使用scroll API获取所有数据
        scroll_size = 1000
        scroll_time = "10m"
        
        response = self.es.search(
            index=config.ES_INDEX,
            body=query,
            scroll=scroll_time,
            size=scroll_size
        )
        
        scroll_id = response['_scroll_id']
        hits = response['hits']['hits']
        total_hits = response['hits']['total']['value'] if isinstance(response['hits']['total'], dict) else response['hits']['total']
        
        logger.info(f"找到 {total_hits} 条数据")
        
        data = {}
        
        # 处理第一批结果
        for hit in hits:
            d = hit['_source']
            # 删除photo字段避免内存问题
            d.pop('photo', None)
            d['_document_id'] = hit['_id']
            
            original_name = d.get('original_name', f"未知名称_{hit['_id']}")
            # 清理名称中的逗号
            cleaned_name = original_name.replace('，', ' ').replace(',', ' ')
            d['original_name'] = cleaned_name
            data[cleaned_name] = d
        
        # 获取剩余数据
        while len(hits) > 0:
            response = self.es.scroll(scroll_id=scroll_id, scroll=scroll_time)
            scroll_id = response['_scroll_id']
            hits = response['hits']['hits']
            
            for hit in hits:
                d = hit['_source']
                d.pop('photo', None)
                d['_document_id'] = hit['_id']
                
                original_name = d.get('original_name', f"未知名称_{hit['_id']}")
                cleaned_name = original_name.replace('，', ' ').replace(',', ' ')
                d['original_name'] = cleaned_name
                data[cleaned_name] = d
        
        logger.info(f"成功获取 {len(data)} 条数据")
        return data
    
    def update_document(self, document_id: str, update_data: Dict) -> bool:
        """更新文档"""
        try:
            # 复制数据避免修改原始数据
            data_to_update = copy.deepcopy(update_data)
            
            # 移除文档ID字段
            data_to_update.pop("_document_id", None)
            
            # 添加更新时间
            data_to_update["update_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            response = self.es.update(
                index=config.ES_INDEX,
                id=document_id,
                body={"doc": data_to_update},
                request_timeout=180
            )
            
            logger.info(f"文档 {document_id} 更新成功: {response['result']}")
            return True
            
        except Exception as e:
            logger.error(f"更新文档 {document_id} 时出错: {e}")
            return False

class WikiAPIClient:
    """Wiki API客户端"""
    
    @staticmethod
    def get_wiki_data(name: str, country: str) -> Optional[str]:
        """获取维基百科数据"""
        try:
            url = f"{config.WIKI_API_BASE_URL}/{name}?country={country}"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            return data
            
        except Exception as e:
            logger.warning(f"获取 {name} 的维基数据失败: {e}")
            return None

class ImageHandler:
    """图片处理类"""
    
    @staticmethod
    def get_image_base64(image_url: str) -> Optional[str]:
        """获取图片的Base64编码"""
        if not image_url:
            return None
        
        try:
            full_url = f"http://192.168.30.135:8080/wiki/image/{image_url}"
            response = requests.get(full_url, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result.get("image_base64")
            
        except Exception as e:
            logger.warning(f"获取图片 {image_url} 失败: {e}")
            return None

def ensure_complete_fields(target_data: Dict) -> Dict:
    """确保数据字段完整"""
    try:
        target_data.pop("_document_id", None)
    except:
        pass
    
    def merge_fields(source_dict: Dict, template_dict: Dict) -> Dict:
        """递归合并字段"""
        result = {}
        for key, template_value in template_dict.items():
            if key in source_dict:
                if isinstance(template_value, dict) and isinstance(source_dict[key], dict):
                    result[key] = merge_fields(source_dict[key], template_value)
                elif isinstance(template_value, list) and isinstance(source_dict[key], list):
                    result_list = []
                    for item in source_dict[key]:
                        if isinstance(item, dict) and template_value:
                            result_list.append(merge_fields(item, template_value[0]))
                        else:
                            result_list.append(item)
                    result[key] = result_list
                else:
                    result[key] = source_dict[key]
            else:
                result[key] = copy.deepcopy(template_value)
        return result
    
    return merge_fields(target_data, config.PERSON_FIELDS_TEMPLATE)

def save_to_csv(name: str, status: str, message: str, document_id: str, image_url: str = ""):
    """保存结果到CSV"""
    import csv
    import os
    
    csv_path = f"{config.RESULTS_DIR}/{config.CSV_FILENAME}"
    
    # 如果文件不存在，创建并写入表头
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(["目标名称", "处理状态", "消息", "文档ID", "图片URL", "处理时间"])
    
    # 追加数据
    with open(csv_path, 'a', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow([name, status, message, document_id, image_url, datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
