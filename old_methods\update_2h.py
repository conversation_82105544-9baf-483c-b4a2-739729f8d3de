# periodic_update.py：修改文件名以反映新的执行频率
import json
import os
import time
import logging
import copy
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from elasticsearch import Elasticsearch
from tqdm import tqdm
from api import DifyAPI, update_photo

# 状态文件路径
STATE_FILE = "/home/<USER>/wiki_update/update_state.json"


def load_state():
    """加载上次执行的状态"""
    default_state = {
        "last_end_timestamp": 1751376543000,
        "last_end_datetime": "2025-07-01 21:29:03"
    }

    if not os.path.exists(STATE_FILE):
        return default_state

    try:
        with open(STATE_FILE, 'r') as f:
            return json.load(f)
    except:
        return default_state


def save_state(end_timestamp, end_datetime):
    """保存执行状态"""
    state = {
        "last_end_timestamp": end_timestamp,
        "last_end_datetime": end_datetime
    }
    with open(STATE_FILE, 'w') as f:
        json.dump(state, f)


# 设置日志
def setup_logger(log_dir: str, log_name: str):
    """设置日志记录器

    Args:
        log_dir (str): 日志目录
        log_name (str): 日志文件名

    Returns:
        logging.Logger: 日志记录器
    """
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f"{log_name}.log")

    logger = logging.getLogger('weekly_updater')
    logger.setLevel(logging.INFO)

    # 清除之前的处理程序
    if logger.handlers:
        logger.handlers.clear()

    # 添加文件处理程序
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 添加控制台处理程序
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理程序到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# 初始化 Dify API 客户端
dify_client = DifyAPI()


# 连接 Elasticsearch
def connect_elasticsearch():
    """连接 Elasticsearch 数据库

    Returns:
        Elasticsearch: Elasticsearch 客户端实例
    """
    try:
        es = Elasticsearch("http://90.90.69.60:9200", http_auth=('root', 'lenovo123456!'),
                           timeout=120, max_retries=2, retry_on_timeout=True)
        if not es.ping():
            raise ConnectionError("无法连接到 Elasticsearch 服务器")
        return es
    except Exception as e:
        raise ConnectionError(f"Elasticsearch 连接错误: {e}")


# 获取新增数据
def get_new_data(es, logger, start_timestamp_ms):
    """获取指定时间范围内的新增数据

    Args:
        es: Elasticsearch 客户端实例
        logger: 日志记录器
        start_timestamp_ms (int): 起始时间戳(毫秒)

    Returns:
        dict: 新增数据字典
    """
    # 当前时间戳（毫秒级）
    end_timestamp_ms = int(datetime.now().timestamp() * 1000)

    logger.info(f"获取时间范围数据: {start_timestamp_ms} -> {end_timestamp_ms} "
                f"({(end_timestamp_ms - start_timestamp_ms) / 1000 / 60:.1f}分钟)")

    # 构建查询
    query = {
        "query": {
            "range": {
                "putin_time": {
                    "gte": start_timestamp_ms,
                    "lt": end_timestamp_ms
                }
            }
        }
    }
    # 使用 scroll API 获取所有符合条件的数据
    scroll_size = 1000  # 每次获取的文档数量
    scroll_time = "10m"  # scroll 上下文保持时间

    # 初始化 scroll
    try:
        response = es.search(
            index="3_targetmap_entity",
            body=query,
            scroll=scroll_time,
            size=scroll_size
        )

        scroll_id = response['_scroll_id']
        hits = response['hits']['hits']
        total_hits = response['hits']['total']['value'] if isinstance(response['hits']['total'], dict) else \
            response['hits']['total']

        logger.info(f"找到 {total_hits} 条一周内新增的数据")

        # 初始化结果字典
        new_data = dict()

        # 处理第一批结果
        for i in hits:
            d = i['_source']
            try:
                del d['photo']  # 删除不需要的图片数据
            except:
                pass

            # 保存 document_id 到数据中
            document_id = i['_id']
            d['_document_id'] = document_id

            original_name = d.get('original_name', f"未知名称_{document_id}")
            # 替换中文逗号和英文逗号为空格
            cleaned_name = original_name.replace('，', ' ').replace(',', ' ')
            d['original_name'] = cleaned_name  # 更新数据中的original_name
            new_data[cleaned_name] = d

        # 获取剩余的数据
        while len(hits) > 0:
            response = es.scroll(scroll_id=scroll_id, scroll=scroll_time)
            scroll_id = response['_scroll_id']
            hits = response['hits']['hits']

            for i in hits:
                d = i['_source']
                try:
                    del d['photo']  # 删除不需要的图片数据
                except:
                    pass

                # 保存 document_id 到数据中
                document_id = i['_id']
                d['_document_id'] = document_id

                original_name = d.get('original_name', f"未知名称_{document_id}")
                # 替换中文逗号和英文逗号为空格
                cleaned_name = original_name.replace('，', ' ').replace(',', ' ')
                d['original_name'] = cleaned_name  # 更新数据中的original_name
                new_data[cleaned_name] = d

        logger.info(f"成功获取 {len(new_data)} 条一周内新增的数据")
        return new_data

    except Exception as e:
        logger.error(f"获取一周内新增数据失败: {e}")
        raise
    # ... (其余部分保持不变) ...


# 确保数据结构完整
def full_attrs(target_data):
    """确保目标数据包含所有必要的属性字段，并移除未定义的额外字段

    Args:
        target_data (dict): 目标数据

    Returns:
        dict: 仅包含预定义字段的完整目标数据
    """
    try:
        target_data.pop("_document_id", None)
    except Exception as e:
        print(e)

    # 预定义的完整字段模板
    example = {
        "birthday": "",
        "sex": "",
        "race": "",
        "ethnic": "",
        "nationality": "",
        "nick_name": "",
        "original_name": "",
        "political_group": "",
        "education": [{
            "graduate_School": "",
            "major": "",
            "degree": "",
            "diploma": "",
            "education_complete_date": "",
            "education_start_date": "",
        }],
        "working_place": "",
        "mobile_number": "",
        "office_number": "",
        "fax_Number": "",
        "email": "",
        "religion_belief": "",
        "is_christianism": "",
        "language": "",
        "enlistment": "",
        "position":"",
        "working": [{
            "company": "",
            "job": "",
            "department": "",
            "description": "",
            "work_end_date": "",
            "work_start_date": "",
        }]
    }

    def sanitize_data(source_dict, template_dict):
        """递归清理数据，仅保留模板中定义的字段"""
        sanitized_dict = {}
        for key, template_value in template_dict.items():
            if key in source_dict:
                # 处理嵌套字典（如 education/working）
                if isinstance(template_value, dict) and isinstance(source_dict[key], dict):
                    sanitized_dict[key] = sanitize_data(source_dict[key], template_value)
                # 处理嵌套列表（如 education:[...]）
                elif isinstance(template_value, list) and isinstance(source_dict[key], list):
                    sanitized_list = []
                    for item in source_dict[key]:
                        if isinstance(item, dict) and template_value:  # 确保模板列表非空
                            sanitized_list.append(sanitize_data(item, template_value[0]))
                    sanitized_dict[key] = sanitized_list
                # 处理普通字段
                else:
                    sanitized_dict[key] = source_dict[key]
            else:
                # 补全缺失字段（直接使用模板默认值）
                sanitized_dict[key] = copy.deepcopy(template_value)
        return sanitized_dict

    # 执行清理和补全
    return sanitize_data(target_data, example)


# 调用 Dify API
def dify_api(target,country, raw_json):
    """调用 Dify API 获取数据

    Args:
        target (str): 目标名称
        country (str): 国家
        raw_json (dict): 目标原始数据

    Returns:
        dict: API 响应结果, 包含状态和数据
    """
    try:
        # 使用 DifyAPI 类发送请求
        response = dify_client.chat(target,country, raw_json)

        # 解析响应
        result = dify_client.parse_response(response)

        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"API 调用异常: {e}",
            "data": None
        }


# 更新 ES 文档
def update_es_document(es, document_id, update_data, request_timeout=180):
    """更新 Elasticsearch 中的文档

    Args:
        es: Elasticsearch 客户端实例
        document_id (str): 文档ID
        update_data (dict): 更新的数据
        request_timeout (int): 请求超时时间

    Returns:
        bool: 更新是否成功
    """
    try:
        # 复制数据以避免修改原始数据
        data_to_update = copy.deepcopy(update_data)

        # 移除文档 ID 字段，避免更新冲突
        if "_document_id" in data_to_update:
            del data_to_update["_document_id"]

        # 添加更新时间
        data_to_update["update_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 更新文档
        response = es.update(
            index="3_targetmap_entity",
            id=document_id,
            body={"doc": data_to_update},
            request_timeout=request_timeout
        )

        return True

    except Exception as e:
        return False


# 主函数
def main():
    # 加载上次执行状态
    state = load_state()
    start_timestamp_ms = state["last_end_timestamp"]
    start_datetime_str = state["last_end_datetime"]

    # 如果是首次运行，使用2小时前的时间
    if start_timestamp_ms == 0:
        two_hours_ago = datetime.now() - timedelta(hours=2)
        start_timestamp_ms = int(two_hours_ago.timestamp() * 1000)
        start_datetime_str = two_hours_ago.strftime("%Y-%m-%d %H:%M:%S")

    # 创建时间戳
    current_datetime = datetime.now()
    timestamp = current_datetime.strftime("%Y%m%d_%H%M%S")
    SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

    # 创建结果目录
    results_dir = os.path.join(SCRIPT_DIR, f"periodic_results/results_{timestamp}")
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)

    # 设置日志
    logger = setup_logger(results_dir, f"periodic_update_{timestamp}")

    logger.info("开始周期性更新任务")
    logger.info(f"上次结束时间: {start_datetime_str} (时间戳: {start_timestamp_ms})")
    logger.info(f"结果将保存在 {results_dir} 目录下")

    try:
        # 连接 Elasticsearch
        logger.info("正在连接 Elasticsearch...")
        es = connect_elasticsearch()
        logger.info("Elasticsearch 连接成功")

        # 获取新增的数据
        logger.info("正在获取新增数据...")
        new_data = get_new_data(es, logger, start_timestamp_ms)

        if not new_data:
            logger.info("没有找到一周内新增的数据，任务结束")
            return

        # 保存新增数据到文件
        new_data_file = os.path.join(results_dir, "new_data.json")
        with open(new_data_file, 'w', encoding='utf8') as f:
            f.write(json.dumps(new_data, ensure_ascii=False))
        logger.info(f"新增数据已保存到 {new_data_file}")

        # 设置统计文件路径
        stats_file = os.path.join(results_dir, f"update_stats_{timestamp}.csv")

        # 创建统计文件头
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("目标名称,处理状态,消息,文档ID,image_url\n")

        # 初始化统计信息
        stats = {
            "total": 0,
            "success": 0,
            "not_same_person": 0,
            "error": 0,
            "unknown": 0
        }

        # 获取所有目标名称
        names = list(new_data.keys())
        total_targets = len(names)

        logger.info(f"开始处理 {total_targets} 个新增目标")

        # 记录开始时间
        start_time = time.time()

        # 使用tqdm创建进度条
        with tqdm(total=total_targets, desc="处理进度", unit="目标") as pbar:
            # 处理每个目标
            for idx, name in enumerate(names, 1):
                logger.info(f"处理第 {idx}/{total_targets} 个目标: {name}")

                stats["total"] += 1

                try:
                    # 获取目标的原始数据
                    target_obj = new_data[name]
                    document_id = target_obj.get("_document_id")
                    country = target_obj.get("nationality")
                    if not document_id:
                        logger.warning(f"目标 {name} 缺少文档ID，跳过")
                        status = "error"
                        message = "缺少文档ID"
                    else:
                        # 确保数据结构完整
                        target_obj_with_full_attrs = full_attrs(target_obj)

                        # 调用 Dify API
                        api_result = dify_api(name, country, target_obj_with_full_attrs)
                        message = api_result.get('message')
                        status = api_result["status"]

                        # 根据 API 结果处理
                        if status == "success" and api_result["data"]:
                            # 合并原始数据和 API 返回的数据
                            if isinstance(api_result["data"], dict):
                                # 直接合并字典,以api_result为主，并将image_url以base64形式导入
                                updated_data = {**target_obj_with_full_attrs, **api_result["data"]}
                                image_url = api_result.get("image_url", None)
                                if image_url != None:
                                    updated_data['photo'] = update_photo(image_url)
                                    message += f"{message} - {name}有图片"
                                else:
                                    message += f"{message} - {name}无图片"
                            else:
                                # 如果不是字典，进一步处理或保存原始响应
                                try:
                                    result = json.loads(api_result["data"])
                                    inner_json = json.loads(result["text"])
                                    updated_data = {**target_obj_with_full_attrs, **inner_json}
                                    image_url = api_result.get("image_url", None)
                                    if image_url != None:
                                        updated_data['photo'] = update_photo(image_url)
                                        message += f"{message} - {name}有图片"
                                    else:
                                        message += f"{message} - {name}无图片"
                                except:
                                    updated_data = target_obj_with_full_attrs
                                    image_url = api_result.get("image_url", None)
                                    message = f"{message} - 未更新-解析response失败！"
                                    status = "error"

                            # 更新 ES 文档
                            if update_es_document(es, document_id, updated_data):
                                stats["success"] += 1
                                # 保存更新后的数据到结果目录
                                safe_name = "".join([c if c.isalnum() or c in "._- " else "_" for c in name])
                                with open(os.path.join(results_dir, f"{safe_name}_updated.json"), 'w',
                                          encoding='utf-8') as f:
                                    json.dump(updated_data, f, ensure_ascii=False, indent=2)
                            else:
                                status = "error"
                                message = "ES更新失败"
                                stats["error"] += 1

                        elif status == "not_same_person":
                            stats["not_same_person"] += 1

                        elif status == "error":
                            stats["error"] += 1

                        else:
                            stats["unknown"] += 1

                    # 记录当前目标的处理结果
                    with open(stats_file, 'a', encoding='utf-8') as f:
                        # 获取image_url值，如果不存在则为空字符串
                        img_url = api_result.get("image_url", "") if 'api_result' in locals() and isinstance(api_result,
                                                                                                             dict) else ""
                        f.write(f"{name},{status},{message},{document_id},{img_url}\n")

                    # 每处理10个目标，输出一次统计信息
                    if idx % 10 == 0 or idx == total_targets:
                        # 计算剩余时间估计
                        elapsed_time = time.time() - start_time
                        items_processed = idx
                        items_per_second = items_processed / elapsed_time if elapsed_time > 0 else 0
                        remaining_items = total_targets - idx
                        remaining_time = remaining_items / items_per_second if items_per_second > 0 else 0

                        # 格式化剩余时间
                        hours, remainder = divmod(remaining_time, 3600)
                        minutes, seconds = divmod(remainder, 60)
                        remaining_time_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"

                        logger.info(f"进度: {idx}/{total_targets}, "
                                    f"成功: {stats['success']}, "
                                    f"不匹配: {stats['not_same_person']}, "
                                    f"错误: {stats['error']}, "
                                    f"未知: {stats['unknown']}, "
                                    f"预计剩余时间: {remaining_time_str}")

                    # 更新进度条
                    pbar.update(1)
                    pbar.set_postfix({
                        "成功": stats['success'],
                        "错误": stats['error'],
                        "不匹配": stats['not_same_person']
                    })

                    # 简单的速率限制，避免过于频繁请求 API
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"处理目标 {name} 时发生异常: {e}")
                    # 记录错误
                    with open(stats_file, 'a', encoding='utf-8') as f:
                        f.write(f"{name},exception,{str(e)},未知\n")
                    stats["error"] += 1

                    # 更新进度条
                    pbar.update(1)

        # 处理完成，输出最终统计
        total_elapsed_time = time.time() - start_time
        hours, remainder = divmod(total_elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        elapsed_time_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"

        logger.info("所有目标处理完成!")
        logger.info(f"统计信息: 总计: {stats['total']}, "
                    f"成功: {stats['success']}, "
                    f"不匹配: {stats['not_same_person']}, "
                    f"错误: {stats['error']}, "
                    f"未知: {stats['unknown']}")
        logger.info(f"总耗时: {elapsed_time_str}")
        logger.info(f"详细结果保存在 {stats_file}")


    except Exception as e:
        logger.error(f"任务执行过程中发生错误: {e}")
    finally:
        # 无论成功与否，更新状态文件
        end_timestamp_ms = int(datetime.now().timestamp() * 1000)
        end_datetime_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        save_state(end_timestamp_ms, end_datetime_str)
        logger.info(f"保存状态: 下次起始时间 {end_datetime_str} (时间戳: {end_timestamp_ms})")

    logger.info("周期性更新任务结束")


if __name__ == "__main__":
    main()