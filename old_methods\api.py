import json
from json import JSONDecodeError
import requests
import logging,re,base64

# 配置日志记录器
logger = logging.getLogger('dify_api')


def update_photo(image_url):
    """
    从指定URL获取图片的Base64编码数据
    Args:
        image_url (str): 图片的相对路径或标识符
    Returns:
        str: 图片的Base64编码数据，失败时返回None
    """
    if not image_url:
        print("错误: 图片URL不能为空")
        return None

    # 构造完整的请求URL
    full_url = f"http://192.168.30.135:8080/wiki/image/{image_url}"

    try:
        # 发送HTTP请求
        response = requests.get(
            full_url
        )
        # 检查HTTP状态码
        response.raise_for_status()
        # 尝试解析JSON响应
        try:
            res = response.json()
        except JSONDecodeError as json_err:
            print(f"JSON解析错误: {json_err}")
            print(f"响应内容: {response.text[:200]}...")  # 打印前200个字符
            return None

        # 获取base64数据
        image_base64 = res.get("image_base64")
        if not image_base64:
            print("错误: 响应中缺少image_base64字段")
            return None
        # 可选: 验证base64数据是否有效
        return image_base64

    except Exception as err:
        print(f"未知错误: {err}")

    return None
class DifyAPI:
    """Dify API 客户端类"""

    def __init__(self, base_url="http://***************", api_key="app-DO5E8EtkvPO6lyIup0OjqYhY"):
        """初始化 Dify API 客户端

        Args:
            base_url (str): API 基础 URL
            api_key (str): API 密钥
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def chat(self, target,country, target_raw_json, query="start", user_id="abc-123", timeout=600):
        """发送聊天请求到 Dify API

        Args:
            target (str): 目标名称
            target_raw_json (dict): 目标原始数据
            query (str): 查询内容
            user_id (str): 用户ID
            timeout (int): 请求超时时间（秒）

        Returns:
            dict: API 响应
        """
        url = f"{self.base_url}/v1/chat-messages"

        # 如果输入是字典，则转换为 JSON 字符串
        if isinstance(target_raw_json, dict):
            target_raw_json_str = json.dumps(target_raw_json, ensure_ascii=False)
        else:
            target_raw_json_str = target_raw_json

        payload = {
            "inputs": {
                "target": target,
                "country":country,
                "target_raw_json": target_raw_json_str
            },
            "query": query,
            "response_mode": "blocking",
            "conversation_id": "",
            "user": user_id,
            "files": []
        }

        try:
            response = requests.post(url, headers=self.headers, json=payload, timeout=timeout)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"API 请求失败: {e}")
            return {"error": str(e)}

    def parse_response(self, response):
        """解析 API 响应

        Args:
            response (dict): API 响应对象

        Returns:
            dict: 包含状态和数据的字典
        """
        answer_text = response.get('answer', '')
        if answer_text and "Could not retrieve any Wikipedia information" in answer_text:
            return {
                "status": "error",
                "message": answer_text,
                "data": answer_text
            }

        # 检查是否包含 "not the same person" 信息
        elif answer_text and "not the same person" in answer_text.lower():
            return {
                "status": "not_same_person",
                "message": "API 返回：目标不是同一个人",
                "data": answer_text
            }

        # 尝试从响应中提取数据
        try:
            if response.get('answer'):
                json_str = response['answer']
                if isinstance(json_str, str):
                    # 首先解析外层JSON
                    outer_json = json.loads(json_str)
                    # 提取image URL
                    image_url = outer_json["image"]
                    # 从text字段中提取内层JSON
                    inner_json_match = re.search(r'```json\n(.+?)\n```', outer_json["text"], re.DOTALL)
                    if inner_json_match:
                        try:
                            inner_json_str = inner_json_match.group(1)
                            inner_json = json.loads(inner_json_str)
                            return {
                                "status": "success",
                                "message": "成功更新数据",
                                "data": inner_json,
                                "image_url": image_url
                            }
                        except json.JSONDecodeError:
                            pass

                # 如果无法解析为 JSON，返回原始响应
                return {
                    "status": "success",
                    "message": "成功更新数据，但格式可能需要进一步处理",
                    "data": json_str,
                    "image_url": None
                }
        except Exception as e:
            logger.warning(f"解析 API 响应时出错: {e}")

        # 默认返回
        return {
            "status": "unknown",
            "message": "接收到响应，但无法确定状态",
            "data": response,
            "image_url": None
        }


# 示例用法
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建 API 客户端实例
    dify_client = DifyAPI()

    # 测试请求
    target_name = "蔡徐坤"
    target_data = {"name":"蔡徐坤","label":"明星","history":""}  # 这里可以放实际数据

    try:
        # 发送请求
        response = dify_client.chat(target_name, target_data)

        # 解析响应
        result = dify_client.parse_response(response)

        # 输出结果
        print(f"状态: {result['status']}")
        print(f"消息: {result['message']}")
        print("数据:")
        print(json.dumps(result['data'], ensure_ascii=False, indent=2) if result['data'] else "无数据")
        print(update_photo(result.get("image_url")))
    except Exception as e:
        print(f"请求出错: {e}")